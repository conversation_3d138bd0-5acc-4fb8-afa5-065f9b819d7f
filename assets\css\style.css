/* ===== CSS VARIABLES ===== */
:root {
    --primary-blue: #1a3c6e;
    --coral: #ff6f61;
    --gold: #f5c71a;
    --white: #ffffff;
    --dark-gray: #333333;
    --light-gray: #f8f9fa;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* ===== GLOBAL STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Roboto', sans-serif;
    line-height: 1.6;
    color: var(--dark-gray);
    overflow-x: hidden;
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    line-height: 1.2;
}

.section-padding {
    padding: 100px 0;
}

.section-title {
    font-size: 2.5rem;
    color: var(--primary-blue);
    margin-bottom: 3rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--coral);
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.fade-in {
    animation: fadeInUp 1s ease-out;
}

.fade-in:nth-child(2) {
    animation-delay: 0.2s;
}

.fade-in:nth-child(3) {
    animation-delay: 0.4s;
}

/* ===== HEADER & NAVIGATION ===== */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    transition: var(--transition);
}

.navbar {
    padding: 1rem 0;
    transition: var(--transition);
}

.navbar.scrolled {
    padding: 0.5rem 0;
    box-shadow: var(--shadow);
}

.logo {
    max-height: 50px;
    width: auto;
}

.navbar-nav .nav-link {
    color: var(--dark-gray);
    font-weight: 500;
    margin: 0 1rem;
    transition: var(--transition);
    position: relative;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--coral);
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--coral);
    transition: var(--transition);
}

.navbar-nav .nav-link:hover::after,
.navbar-nav .nav-link.active::after {
    width: 100%;
}

/* ===== HERO SECTION ===== */
.hero {
    position: relative;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.hero-video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
}

.hero-video video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(26, 60, 110, 0.5);
    z-index: -1;
}

.hero-content {
    text-align: center;
    color: var(--white);
    z-index: 1;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-btn {
    background: var(--coral);
    border: none;
    padding: 15px 40px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    transition: var(--transition);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.hero-btn:hover {
    background: var(--gold);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(255, 111, 97, 0.3);
}

/* ===== ABOUT SECTION ===== */
.about {
    background: var(--white);
}

.about-text {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    text-align: justify;
}

.about-image img {
    border-radius: 10px;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.about-image img:hover {
    transform: scale(1.05);
}

/* ===== SERVICES SECTION ===== */
.services {
    background: var(--light-gray);
}

.service-card {
    background: var(--white);
    padding: 3rem 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: var(--transition);
    height: 100%;
    border: 2px solid transparent;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
    border-color: var(--coral);
}

.service-icon {
    font-size: 2.5rem;
    color: var(--coral);
    margin-bottom: 1.5rem;
}

.service-title {
    color: var(--primary-blue);
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.service-description {
    color: var(--dark-gray);
    line-height: 1.6;
}

/* ===== PORTFOLIO SECTION ===== */
.portfolio {
    background: var(--white);
}

.portfolio-filters {
    margin-bottom: 3rem;
}

.filter-btn {
    background: transparent;
    border: 2px solid var(--primary-blue);
    color: var(--primary-blue);
    padding: 10px 25px;
    margin: 0 10px 10px 0;
    border-radius: 25px;
    font-weight: 500;
    transition: var(--transition);
    cursor: pointer;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--primary-blue);
    color: var(--white);
}

.portfolio-item {
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    margin-bottom: 2rem;
    cursor: pointer;
}

.portfolio-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: var(--transition);
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(26, 60, 110, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.portfolio-item:hover .portfolio-overlay {
    opacity: 1;
}

.portfolio-item:hover img {
    transform: scale(1.1);
}

.portfolio-overlay a {
    color: var(--white);
    font-size: 1.2rem;
    font-weight: 600;
    text-decoration: none;
    padding: 10px 20px;
    border: 2px solid var(--white);
    border-radius: 25px;
    transition: var(--transition);
}

.portfolio-overlay a:hover {
    background: var(--white);
    color: var(--primary-blue);
}

/* ===== TESTIMONIALS SECTION ===== */
.testimonials {
    background: var(--light-gray);
}

.testimonial-item {
    background: var(--white);
    padding: 3rem 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: var(--shadow);
    margin: 0 15px;
}

.testimonial-image {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    margin: 0 auto 1.5rem;
    overflow: hidden;
}

.testimonial-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.testimonial-quote {
    font-style: italic;
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    color: var(--dark-gray);
}

.testimonial-author {
    font-weight: 600;
    color: var(--primary-blue);
    margin-bottom: 0.5rem;
}

.testimonial-company {
    color: var(--coral);
    font-size: 0.9rem;
}

/* ===== CONTACT SECTION ===== */
.contact {
    background: var(--white);
}

.contact-form .form-control {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 15px 20px;
    font-size: 1rem;
    transition: var(--transition);
}

.contact-form .form-control:focus {
    border-color: var(--coral);
    box-shadow: 0 0 0 0.2rem rgba(255, 111, 97, 0.25);
}

.contact-form .btn-primary {
    background: var(--primary-blue);
    border: none;
    padding: 15px 40px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    transition: var(--transition);
}

.contact-form .btn-primary:hover {
    background: var(--coral);
    transform: translateY(-2px);
}

.contact-icon {
    font-size: 2rem;
    color: var(--coral);
    margin-bottom: 1rem;
}

.contact-info p {
    color: var(--dark-gray);
    font-weight: 500;
}

.social-links {
    margin-top: 2rem;
}

.social-link {
    display: inline-block;
    width: 50px;
    height: 50px;
    line-height: 50px;
    text-align: center;
    background: var(--gold);
    color: var(--white);
    border-radius: 50%;
    margin: 0 10px;
    font-size: 1.2rem;
    transition: var(--transition);
    text-decoration: none;
}

.social-link:hover {
    background: var(--primary-blue);
    color: var(--white);
    transform: translateY(-3px);
}

/* ===== FOOTER ===== */
.footer {
    background: var(--primary-blue);
    color: var(--white);
    padding: 3rem 0 1rem;
}

.footer-logo {
    max-height: 40px;
    width: auto;
}

.footer-text {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 1rem;
}

.footer-title {
    color: var(--white);
    margin-bottom: 1.5rem;
    font-weight: 600;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--coral);
}

.footer-social-link {
    display: inline-block;
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background: var(--gold);
    color: var(--white);
    border-radius: 50%;
    margin: 0 10px 10px 0;
    font-size: 1rem;
    transition: var(--transition);
    text-decoration: none;
}

.footer-social-link:hover {
    background: var(--coral);
    color: var(--white);
    transform: translateY(-2px);
}

.footer-divider {
    border-color: rgba(255, 255, 255, 0.2);
    margin: 2rem 0 1rem;
}

.footer-copyright {
    color: rgba(255, 255, 255, 0.6);
    margin: 0;
    font-size: 0.9rem;
}

/* ===== BACK TO TOP BUTTON ===== */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--coral);
    color: var(--white);
    border: none;
    border-radius: 50%;
    font-size: 1.2rem;
    cursor: pointer;
    transition: var(--transition);
    opacity: 0;
    visibility: hidden;
    z-index: 1000;
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background: var(--gold);
    transform: translateY(-3px);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .section-padding {
        padding: 60px 0;
    }
    
    .service-card {
        margin-bottom: 2rem;
    }
    
    .navbar-nav .nav-link {
        margin: 0.5rem 0;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 1.8rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .hero-btn {
        padding: 12px 30px;
        font-size: 1rem;
    }

    .service-card {
        padding: 2rem 1.5rem;
    }
}

/* ===== LIGHTBOX STYLES ===== */
.lightbox-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    animation: fadeIn 0.3s ease forwards;
}

.lightbox-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    background: var(--white);
    border-radius: 10px;
    overflow: hidden;
    transform: scale(0.8);
    animation: scaleIn 0.3s ease forwards;
}

@keyframes scaleIn {
    to {
        transform: scale(1);
    }
}

.lightbox-close {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 2rem;
    color: var(--white);
    cursor: pointer;
    z-index: 2001;
    background: rgba(0, 0, 0, 0.5);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.lightbox-close:hover {
    background: var(--coral);
}

.lightbox-image {
    width: 100%;
    max-height: 70vh;
    object-fit: cover;
}

.lightbox-info {
    padding: 2rem;
    text-align: center;
}

.lightbox-info h3 {
    color: var(--primary-blue);
    margin-bottom: 1rem;
}

.lightbox-info p {
    color: var(--dark-gray);
    margin: 0;
}

/* ===== TESTIMONIAL CAROUSEL STYLES ===== */
.testimonial-slider {
    position: relative;
    overflow: hidden;
}

.testimonial-item {
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.5s ease;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
}

.testimonial-item.active {
    opacity: 1;
    transform: translateX(0);
    position: relative;
}

.testimonial-nav {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 2rem;
    gap: 2rem;
}

.testimonial-prev,
.testimonial-next {
    background: var(--coral);
    color: var(--white);
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    font-size: 1.2rem;
    cursor: pointer;
    transition: var(--transition);
}

.testimonial-prev:hover,
.testimonial-next:hover {
    background: var(--primary-blue);
    transform: scale(1.1);
}

.testimonial-dots {
    display: flex;
    gap: 10px;
}

.testimonial-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #ddd;
    cursor: pointer;
    transition: var(--transition);
}

.testimonial-dot.active {
    background: var(--coral);
}

.testimonial-dot:hover {
    background: var(--primary-blue);
}

/* ===== FORM VALIDATION STYLES ===== */
.form-control.is-invalid {
    border-color: #dc3545;
}

.form-control.is-valid {
    border-color: #28a745;
}

.invalid-feedback {
    display: block;
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.valid-feedback {
    display: block;
    color: #28a745;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* ===== LOADING ANIMATION ===== */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--white);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* ===== LAZY LOADING ===== */
img.lazy {
    opacity: 0;
    transition: opacity 0.3s;
}

img.lazy.loaded {
    opacity: 1;
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles for better accessibility */
.btn:focus,
.form-control:focus,
.nav-link:focus {
    outline: 2px solid var(--coral);
    outline-offset: 2px;
}

/* ===== PRINT STYLES ===== */
@media print {
    .navbar,
    .back-to-top,
    .hero-video {
        display: none !important;
    }

    .hero {
        height: auto;
        background: var(--white);
        color: var(--dark-gray);
    }

    .section-padding {
        padding: 2rem 0;
    }
}

# VideoEdit Pro - Professional Video Editing Website

A modern, responsive website for a video editing company built with HTML5, CSS3, JavaScript, and Bootstrap 5. This website replicates the structure and functionality of menofvision.in with a custom video editing theme and color scheme.

## 🎨 Design Features

- **Color Scheme**: Deep blue (#1a3c6e), <PERSON> (#ff6f61), <PERSON> (#f5c71a), <PERSON> (#ffffff), <PERSON> gray (#333333)
- **Typography**: Poppins for headings, Roboto for body text
- **Responsive Design**: Mobile-first approach with Bootstrap 5
- **Smooth Animations**: Fade-in effects, hover transitions, and scroll animations
- **Modern UI**: Clean, professional design with video editing focus

## 🚀 Features

### Header & Navigation
- Sticky navigation bar with smooth scrolling
- Responsive hamburger menu for mobile
- Active link highlighting based on scroll position
- Logo placeholder for client branding

### Hero Section
- Full-screen video background with fallback image
- Animated text overlay with call-to-action button
- Responsive typography and mobile optimization

### About Section
- Two-column layout with company information
- Team/studio image with hover effects
- Scroll-triggered animations

### Services Section
- Grid layout with 4 service cards
- Font Awesome icons with hover effects
- Professional service descriptions
- Responsive card layout

### Portfolio Section
- Masonry grid layout for project showcase
- Category filtering (All, Corporate, Commercial, Social Media)
- Lightbox modal for project details
- Hover overlays with smooth transitions

### Testimonials Section
- Carousel slider with client testimonials
- Navigation arrows and dot indicators
- Auto-play functionality
- Client photos and company information

### Contact Section
- Professional contact form with validation
- Contact information display
- Social media links
- Form submission handling

### Footer
- Company information and quick links
- Social media icons
- Copyright notice
- Responsive layout

## 📁 Project Structure

```
VideoEdit Pro/
├── index.html              # Main HTML file
├── assets/
│   ├── css/
│   │   └── style.css       # Custom CSS styles
│   ├── js/
│   │   └── script.js       # JavaScript functionality
│   ├── images/             # Image assets
│   │   ├── logo.png        # Main logo
│   │   ├── logo-white.png  # White logo for footer
│   │   ├── about-team.jpg  # About section image
│   │   ├── portfolio-*.jpg # Portfolio images
│   │   ├── client-*.jpg    # Client testimonial photos
│   │   └── favicon.ico     # Website favicon
│   └── videos/
│       └── hero-video.mp4  # Hero background video
└── README.md               # Project documentation
```

## 🛠️ Technologies Used

- **HTML5**: Semantic markup and structure
- **CSS3**: Custom styling with CSS variables and animations
- **JavaScript (ES6+)**: Interactive functionality and DOM manipulation
- **Bootstrap 5**: Responsive grid system and components
- **Font Awesome**: Icon library
- **Google Fonts**: Poppins and Roboto font families

## 📱 Responsive Breakpoints

- **Desktop**: 1200px and above
- **Tablet**: 768px - 1199px
- **Mobile**: Below 768px

## 🚀 Setup Instructions

### Local Development

1. **Clone or download** the project files to your local machine
2. **Open** `index.html` in your web browser
3. **Replace placeholder images** with your actual content:
   - Add your logo to `assets/images/logo.png`
   - Add team photo to `assets/images/about-team.jpg`
   - Add portfolio images to `assets/images/portfolio-*.jpg`
   - Add client photos to `assets/images/client-*.jpg`
   - Add hero video to `assets/videos/hero-video.mp4`

### Content Customization

1. **Update company information** in `index.html`:
   - Company name and tagline
   - About section content
   - Contact information
   - Social media links

2. **Customize colors** in `assets/css/style.css`:
   - Modify CSS variables in the `:root` section
   - Adjust color scheme to match your brand

3. **Add your content**:
   - Replace placeholder text with your actual content
   - Update service descriptions
   - Add real portfolio items
   - Include genuine client testimonials

## 🌐 Deployment Options

### Option 1: Netlify (Recommended)
1. Create a free account at [netlify.com](https://netlify.com)
2. Drag and drop your project folder to Netlify
3. Your site will be live instantly with a custom URL
4. Connect a custom domain if needed

### Option 2: GitHub Pages
1. Create a GitHub repository
2. Upload your files to the repository
3. Enable GitHub Pages in repository settings
4. Your site will be available at `username.github.io/repository-name`

### Option 3: Traditional Web Hosting
1. Choose a hosting provider (Hostinger, Bluehost, etc.)
2. Upload files via FTP to your hosting account
3. Point your domain to the hosting server
4. Your site will be live at your domain

## 🔧 Customization Guide

### Adding New Portfolio Items
1. Add images to `assets/images/`
2. Update the `portfolioItems` array in `assets/js/script.js`
3. Include category, image filename, title, and description

### Modifying Services
1. Update service cards in the HTML
2. Change Font Awesome icons as needed
3. Adjust service descriptions and titles

### Contact Form Integration
The contact form currently shows a success message. To integrate with a backend:

1. **Formspree Integration**:
   ```html
   <form action="https://formspree.io/f/your-form-id" method="POST">
   ```

2. **Netlify Forms**:
   ```html
   <form name="contact" netlify>
   ```

3. **Custom Backend**: Modify the form submission handler in `script.js`

## 🎯 SEO Optimization

The website includes:
- Semantic HTML structure
- Meta tags for search engines
- Open Graph tags for social sharing
- Alt text for images
- Fast loading optimization
- Mobile-friendly design

## ♿ Accessibility Features

- Keyboard navigation support
- Screen reader friendly markup
- High contrast color scheme
- Focus indicators for interactive elements
- Semantic HTML structure

## 📊 Performance Optimization

- Lazy loading for images
- Optimized CSS and JavaScript
- Compressed image formats recommended
- CDN usage for external libraries
- Minimal HTTP requests

## 🐛 Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Internet Explorer 11+ (limited support)

## 📞 Support

For questions or customization help:
- Review the code comments for guidance
- Check browser developer tools for debugging
- Ensure all file paths are correct
- Verify image formats and sizes

## 📄 License

This project is open source and available under the MIT License. Feel free to use, modify, and distribute as needed for your projects.

---

**Note**: Remember to replace all placeholder content with your actual business information before going live. Test the website thoroughly across different devices and browsers to ensure optimal performance.

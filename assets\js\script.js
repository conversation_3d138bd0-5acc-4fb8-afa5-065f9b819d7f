// ===== GLOBAL VARIABLES =====
let isScrolling = false;

// ===== DOM CONTENT LOADED =====
document.addEventListener('DOMContentLoaded', function() {
    initializeWebsite();
});

// ===== INITIALIZE WEBSITE =====
function initializeWebsite() {
    setupNavigation();
    setupScrollAnimations();
    setupBackToTop();
    setupPortfolio();
    setupTestimonials();
    setupContactForm();
    setupLazyLoading();
}

// ===== NAVIGATION SETUP =====
function setupNavigation() {
    const navbar = document.querySelector('.navbar');
    const navLinks = document.querySelectorAll('.nav-link');
    
    // Navbar scroll effect
    window.addEventListener('scroll', function() {
        if (window.scrollY > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });
    
    // Smooth scrolling for navigation links
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const offsetTop = targetSection.offsetTop - 80;
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
                
                // Update active nav link
                updateActiveNavLink(this);
                
                // Close mobile menu if open
                const navbarCollapse = document.querySelector('.navbar-collapse');
                if (navbarCollapse.classList.contains('show')) {
                    const bsCollapse = new bootstrap.Collapse(navbarCollapse);
                    bsCollapse.hide();
                }
            }
        });
    });
    
    // Update active nav link on scroll
    window.addEventListener('scroll', updateActiveNavOnScroll);
}

// ===== UPDATE ACTIVE NAVIGATION =====
function updateActiveNavLink(activeLink) {
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    activeLink.classList.add('active');
}

function updateActiveNavOnScroll() {
    if (isScrolling) return;
    
    const sections = document.querySelectorAll('section[id]');
    const scrollPos = window.scrollY + 100;
    
    sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.offsetHeight;
        const sectionId = section.getAttribute('id');
        
        if (scrollPos >= sectionTop && scrollPos < sectionTop + sectionHeight) {
            const activeLink = document.querySelector(`.nav-link[href="#${sectionId}"]`);
            if (activeLink) {
                updateActiveNavLink(activeLink);
            }
        }
    });
}

// ===== SCROLL ANIMATIONS =====
function setupScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    const animateElements = document.querySelectorAll('.service-card, .portfolio-item, .testimonial-item, .about-image, .contact-form');
    animateElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
}

// ===== BACK TO TOP BUTTON =====
function setupBackToTop() {
    const backToTopBtn = document.getElementById('backToTop');
    
    window.addEventListener('scroll', function() {
        if (window.scrollY > 200) {
            backToTopBtn.classList.add('show');
        } else {
            backToTopBtn.classList.remove('show');
        }
    });
    
    backToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

// ===== PORTFOLIO SETUP =====
function setupPortfolio() {
    const portfolioGrid = document.querySelector('.portfolio-grid');
    const filterBtns = document.querySelectorAll('.filter-btn');
    
    // Portfolio items data
    const portfolioItems = [
        { category: 'corporate', image: 'portfolio-1.jpg', title: 'Corporate Video', description: 'Professional corporate presentation video' },
        { category: 'commercial', image: 'portfolio-2.jpg', title: 'Product Commercial', description: 'Engaging product advertisement' },
        { category: 'social', image: 'portfolio-3.jpg', title: 'Social Media Content', description: 'Viral social media video' },
        { category: 'corporate', image: 'portfolio-4.jpg', title: 'Training Video', description: 'Employee training content' },
        { category: 'commercial', image: 'portfolio-5.jpg', title: 'Brand Story', description: 'Compelling brand narrative' },
        { category: 'social', image: 'portfolio-6.jpg', title: 'Instagram Reel', description: 'Trendy Instagram content' },
        { category: 'corporate', image: 'portfolio-7.jpg', title: 'Event Coverage', description: 'Corporate event highlights' },
        { category: 'commercial', image: 'portfolio-8.jpg', title: 'TV Commercial', description: 'Television advertisement' }
    ];
    
    // Render portfolio items
    function renderPortfolio(items = portfolioItems) {
        portfolioGrid.innerHTML = '';
        items.forEach((item, index) => {
            const portfolioItem = document.createElement('div');
            portfolioItem.className = `col-lg-4 col-md-6 mb-4 portfolio-item ${item.category}`;
            portfolioItem.innerHTML = `
                <div class="portfolio-item-inner">
                    <img src="./assets/images/${item.image}" alt="${item.title}" class="img-fluid">
                    <div class="portfolio-overlay">
                        <a href="#" onclick="openLightbox('${item.image}', '${item.title}', '${item.description}')">View Project</a>
                    </div>
                </div>
            `;
            portfolioGrid.appendChild(portfolioItem);
        });
    }
    
    // Filter functionality
    filterBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            
            // Update active filter button
            filterBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            // Filter items
            if (filter === 'all') {
                renderPortfolio();
            } else {
                const filteredItems = portfolioItems.filter(item => item.category === filter);
                renderPortfolio(filteredItems);
            }
        });
    });
    
    // Initial render
    renderPortfolio();
}

// ===== LIGHTBOX FUNCTIONALITY =====
function openLightbox(image, title, description) {
    const lightboxHTML = `
        <div class="lightbox-overlay" onclick="closeLightbox()">
            <div class="lightbox-content" onclick="event.stopPropagation()">
                <span class="lightbox-close" onclick="closeLightbox()">&times;</span>
                <img src="./assets/images/${image}" alt="${title}" class="lightbox-image">
                <div class="lightbox-info">
                    <h3>${title}</h3>
                    <p>${description}</p>
                </div>
            </div>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', lightboxHTML);
    document.body.style.overflow = 'hidden';
}

function closeLightbox() {
    const lightbox = document.querySelector('.lightbox-overlay');
    if (lightbox) {
        lightbox.remove();
        document.body.style.overflow = 'auto';
    }
}

// ===== TESTIMONIALS CAROUSEL =====
function setupTestimonials() {
    const testimonialCarousel = document.querySelector('.testimonial-carousel');
    
    const testimonials = [
        {
            image: 'client-1.jpg',
            quote: 'VideoEdit Pro transformed our corporate videos into cinematic masterpieces. Their attention to detail and creative vision exceeded our expectations. The team is professional, responsive, and truly understands our brand.',
            author: 'Sarah Johnson',
            company: 'Tech Innovations Inc.'
        },
        {
            image: 'client-2.jpg',
            quote: 'Working with VideoEdit Pro was an absolute pleasure. They took our raw footage and created something magical. The final product was beyond what we imagined, and the process was smooth from start to finish.',
            author: 'Michael Chen',
            company: 'Creative Studios'
        },
        {
            image: 'client-3.jpg',
            quote: 'The quality of work from VideoEdit Pro is outstanding. They delivered our project on time and within budget. Their expertise in motion graphics and color grading really made our content stand out.',
            author: 'Emily Rodriguez',
            company: 'Marketing Solutions'
        },
        {
            image: 'client-4.jpg',
            quote: 'I highly recommend VideoEdit Pro for any video editing needs. Their team is talented, professional, and easy to work with. They brought our vision to life in ways we never thought possible.',
            author: 'David Thompson',
            company: 'Startup Ventures'
        },
        {
            image: 'client-5.jpg',
            quote: 'VideoEdit Pro consistently delivers high-quality work. Their creative approach and technical expertise have helped us create compelling content that resonates with our audience.',
            author: 'Lisa Wang',
            company: 'Digital Agency'
        }
    ];
    
    // Create carousel HTML
    let carouselHTML = '<div class="testimonial-slider">';
    testimonials.forEach((testimonial, index) => {
        carouselHTML += `
            <div class="testimonial-item ${index === 0 ? 'active' : ''}">
                <div class="testimonial-image">
                    <img src="./assets/images/${testimonial.image}" alt="${testimonial.author}">
                </div>
                <p class="testimonial-quote">"${testimonial.quote}"</p>
                <h5 class="testimonial-author">${testimonial.author}</h5>
                <p class="testimonial-company">${testimonial.company}</p>
            </div>
        `;
    });
    carouselHTML += '</div>';
    
    // Add navigation
    carouselHTML += `
        <div class="testimonial-nav">
            <button class="testimonial-prev" onclick="changeTestimonial(-1)">
                <i class="fas fa-chevron-left"></i>
            </button>
            <div class="testimonial-dots">
                ${testimonials.map((_, index) => `
                    <span class="testimonial-dot ${index === 0 ? 'active' : ''}" onclick="currentTestimonial(${index + 1})"></span>
                `).join('')}
            </div>
            <button class="testimonial-next" onclick="changeTestimonial(1)">
                <i class="fas fa-chevron-right"></i>
            </button>
        </div>
    `;
    
    testimonialCarousel.innerHTML = carouselHTML;
    
    // Auto-play testimonials
    setInterval(() => {
        changeTestimonial(1);
    }, 5000);
}

let currentTestimonialIndex = 0;

function changeTestimonial(direction) {
    const testimonials = document.querySelectorAll('.testimonial-item');
    const dots = document.querySelectorAll('.testimonial-dot');
    
    testimonials[currentTestimonialIndex].classList.remove('active');
    dots[currentTestimonialIndex].classList.remove('active');
    
    currentTestimonialIndex += direction;
    
    if (currentTestimonialIndex >= testimonials.length) {
        currentTestimonialIndex = 0;
    } else if (currentTestimonialIndex < 0) {
        currentTestimonialIndex = testimonials.length - 1;
    }
    
    testimonials[currentTestimonialIndex].classList.add('active');
    dots[currentTestimonialIndex].classList.add('active');
}

function currentTestimonial(index) {
    const testimonials = document.querySelectorAll('.testimonial-item');
    const dots = document.querySelectorAll('.testimonial-dot');
    
    testimonials[currentTestimonialIndex].classList.remove('active');
    dots[currentTestimonialIndex].classList.remove('active');
    
    currentTestimonialIndex = index - 1;
    
    testimonials[currentTestimonialIndex].classList.add('active');
    dots[currentTestimonialIndex].classList.add('active');
}

// ===== CONTACT FORM =====
function setupContactForm() {
    const contactForm = document.getElementById('contactForm');
    
    contactForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const formObject = {};
        formData.forEach((value, key) => {
            formObject[key] = value;
        });
        
        // Simulate form submission
        showFormMessage('Thank you for your message! We will get back to you soon.', 'success');
        this.reset();
    });
}

function showFormMessage(message, type) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'} mt-3`;
    messageDiv.textContent = message;
    
    const form = document.getElementById('contactForm');
    form.appendChild(messageDiv);
    
    setTimeout(() => {
        messageDiv.remove();
    }, 5000);
}

// ===== LAZY LOADING =====
function setupLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');
    
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                observer.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
}

// ===== UTILITY FUNCTIONS =====
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// ===== KEYBOARD NAVIGATION =====
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeLightbox();
    }
});

// ===== PERFORMANCE OPTIMIZATION =====
window.addEventListener('scroll', debounce(function() {
    // Optimized scroll handling
}, 10));

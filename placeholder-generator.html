<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Placeholder Image Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .placeholder {
            margin: 20px 0;
            text-align: center;
            border: 2px dashed #ddd;
            padding: 20px;
            border-radius: 10px;
        }
        canvas {
            border: 1px solid #ddd;
            margin: 10px;
        }
        button {
            background: #1a3c6e;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #ff6f61;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>VideoEdit Pro - Placeholder Image Generator</h1>
        <p>This tool generates placeholder images for the VideoEdit Pro website. Click the buttons below to generate and download the images.</p>
        
        <div class="grid">
            <div class="placeholder">
                <h3>Logo (200x60)</h3>
                <canvas id="logo" width="200" height="60"></canvas>
                <br>
                <button onclick="generateLogo()">Generate Logo</button>
                <button onclick="downloadCanvas('logo', 'logo.png')">Download</button>
            </div>
            
            <div class="placeholder">
                <h3>About Team (600x400)</h3>
                <canvas id="about-team" width="300" height="200"></canvas>
                <br>
                <button onclick="generateAboutTeam()">Generate Image</button>
                <button onclick="downloadCanvas('about-team', 'about-team.jpg')">Download</button>
            </div>
            
            <div class="placeholder">
                <h3>Portfolio Item (400x300)</h3>
                <canvas id="portfolio" width="200" height="150"></canvas>
                <br>
                <button onclick="generatePortfolio()">Generate Portfolio</button>
                <button onclick="downloadCanvas('portfolio', 'portfolio-1.jpg')">Download</button>
            </div>
            
            <div class="placeholder">
                <h3>Client Photo (100x100)</h3>
                <canvas id="client" width="100" height="100"></canvas>
                <br>
                <button onclick="generateClient()">Generate Client</button>
                <button onclick="downloadCanvas('client', 'client-1.jpg')">Download</button>
            </div>
        </div>
        
        <div style="margin-top: 40px; padding: 20px; background: #f8f9fa; border-radius: 10px;">
            <h3>Instructions:</h3>
            <ol>
                <li>Click "Generate" buttons to create placeholder images</li>
                <li>Click "Download" to save the images to your computer</li>
                <li>Place the downloaded images in the <code>assets/images/</code> folder</li>
                <li>Replace with your actual images when ready</li>
            </ol>
            
            <h3>Required Images:</h3>
            <ul>
                <li><strong>logo.png</strong> - Company logo (200x60px)</li>
                <li><strong>logo-white.png</strong> - White version of logo for footer</li>
                <li><strong>about-team.jpg</strong> - Team or studio photo (600x400px)</li>
                <li><strong>portfolio-1.jpg to portfolio-8.jpg</strong> - Portfolio images (400x300px)</li>
                <li><strong>client-1.jpg to client-5.jpg</strong> - Client photos (100x100px)</li>
                <li><strong>favicon.ico</strong> - Website favicon (32x32px)</li>
                <li><strong>og-image.jpg</strong> - Social media preview image (1200x630px)</li>
            </ul>
        </div>
    </div>

    <script>
        function generateLogo() {
            const canvas = document.getElementById('logo');
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Background
            ctx.fillStyle = '#1a3c6e';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Text
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('VideoEdit', canvas.width/2, 25);
            ctx.fillStyle = '#ff6f61';
            ctx.font = 'bold 12px Arial';
            ctx.fillText('PRO', canvas.width/2, 45);
        }
        
        function generateAboutTeam() {
            const canvas = document.getElementById('about-team');
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Background gradient
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#1a3c6e');
            gradient.addColorStop(1, '#ff6f61');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Overlay
            ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Text
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Our Team', canvas.width/2, canvas.height/2 - 10);
            ctx.font = '14px Arial';
            ctx.fillText('Professional Video Editors', canvas.width/2, canvas.height/2 + 15);
        }
        
        function generatePortfolio() {
            const canvas = document.getElementById('portfolio');
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Background
            ctx.fillStyle = '#f5c71a';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Video play icon
            ctx.fillStyle = '#1a3c6e';
            ctx.beginPath();
            ctx.moveTo(canvas.width/2 - 15, canvas.height/2 - 15);
            ctx.lineTo(canvas.width/2 + 15, canvas.height/2);
            ctx.lineTo(canvas.width/2 - 15, canvas.height/2 + 15);
            ctx.closePath();
            ctx.fill();
            
            // Text
            ctx.fillStyle = '#1a3c6e';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Portfolio Item', canvas.width/2, 30);
        }
        
        function generateClient() {
            const canvas = document.getElementById('client');
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Background circle
            ctx.fillStyle = '#ff6f61';
            ctx.beginPath();
            ctx.arc(canvas.width/2, canvas.height/2, 45, 0, 2 * Math.PI);
            ctx.fill();
            
            // Person icon
            ctx.fillStyle = '#ffffff';
            // Head
            ctx.beginPath();
            ctx.arc(canvas.width/2, 35, 12, 0, 2 * Math.PI);
            ctx.fill();
            
            // Body
            ctx.beginPath();
            ctx.arc(canvas.width/2, 70, 18, 0, Math.PI, true);
            ctx.fill();
        }
        
        function downloadCanvas(canvasId, filename) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // Generate initial images
        window.onload = function() {
            generateLogo();
            generateAboutTeam();
            generatePortfolio();
            generateClient();
        };
    </script>
</body>
</html>
